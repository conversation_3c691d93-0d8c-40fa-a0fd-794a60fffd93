'use client'

import { X, Receipt, DollarSign, User, Package, CreditCard, Users, FileText, Eye } from 'lucide-react'
import { useTheme } from 'next-themes'
import { useState, useEffect, useCallback } from 'react'

import { CustomerDebt, CustomerPayment, Customer } from '@/lib/supabase'

import CustomerProfile from './CustomerProfile'
import ProfilePictureUpload from './ProfilePictureUpload'

interface CustomerDebtDetailsModalProps {
  isOpen: boolean
  onClose: () => void
  customerName: string
  customerFamilyName: string
  onRefresh?: () => void // Optional callback to refresh parent component
}

export default function CustomerDebtDetailsModal({
  isOpen,
  onClose,
  customerName,
  customerFamilyName,
  onRefresh
}: CustomerDebtDetailsModalProps) {
  const { resolvedTheme } = useTheme()
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [debts, setDebts] = useState<CustomerDebt[]>([])
  const [payments, setPayments] = useState<CustomerPayment[]>([])
  const [customer, setCustomer] = useState<Customer | null>(null)
  const [activeTab, setActiveTab] = useState<'profile' | 'debts' | 'payments' | 'summary'>('profile')

  // Sukli confirmation dialog state
  const [sukliDialog, setSukliDialog] = useState<{
    isOpen: boolean
    amount: number
  }>({
    isOpen: false,
    amount: 0
  })

  const fetchCustomerData = useCallback(async () => {
    setLoading(true)
    setError(null)

    try {
      // Try multiple approaches to find the customer profile
      let foundCustomer: Customer | null = null

      // Approach 1: Search by exact name match
      const exactSearchResponse = await fetch(
        `/api/customers?search=${encodeURIComponent(customerName + ' ' + customerFamilyName)}`
      )

      if (exactSearchResponse.ok) {
        const exactSearchData = await exactSearchResponse.json()
        
        foundCustomer = exactSearchData.data?.customers?.find((c: Customer) =>
          c.customer_name === customerName && c.customer_family_name === customerFamilyName
        )
      }

      // Approach 2: If not found, try searching by individual names
      if (!foundCustomer) {
        const individualSearchResponse = await fetch(
          `/api/customers?search=${encodeURIComponent(customerName)}`
        )

        if (individualSearchResponse.ok) {
          const individualSearchData = await individualSearchResponse.json()
          
          foundCustomer = individualSearchData.data?.customers?.find((c: Customer) =>
            c.customer_name === customerName && c.customer_family_name === customerFamilyName
          )
        }
      }

      // Approach 3: If still not found, get all customers and filter
      if (!foundCustomer) {
        const allCustomersResponse = await fetch('/api/customers?limit=1000')
        
        if (allCustomersResponse.ok) {
          const allCustomersData = await allCustomersResponse.json()
          
          foundCustomer = allCustomersData.data?.customers?.find((c: Customer) =>
            c.customer_name === customerName && c.customer_family_name === customerFamilyName
          )
        }
      }

      setCustomer(foundCustomer || null)

      // Fetch debts
      const debtsResponse = await fetch(
        `/api/debts?customer_name=${encodeURIComponent(customerName)}&customer_family_name=${encodeURIComponent(customerFamilyName)}`
      )

      if (!debtsResponse.ok) throw new Error('Failed to fetch debts')
      const debtsData = await debtsResponse.json()
      setDebts(debtsData.data?.debts || [])

      // Fetch payments
      const paymentsResponse = await fetch(
        `/api/payments?customer_name=${encodeURIComponent(customerName)}&customer_family_name=${encodeURIComponent(customerFamilyName)}`
      )

      if (!paymentsResponse.ok) throw new Error('Failed to fetch payments')
      const paymentsData = await paymentsResponse.json()
      setPayments(paymentsData.data?.payments || [])

    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch customer data')
    } finally {
      setLoading(false)
    }
  }, [customerName, customerFamilyName])

  // Fetch customer debts and payments
  useEffect(() => {
    if (isOpen && customerName && customerFamilyName) {
      fetchCustomerData()
    }
  }, [isOpen, customerName, customerFamilyName, fetchCustomerData])

  // Handle sukli click
  const handleSukliClick = (_customerName: string, _customerFamilyName: string, amount: number) => {
    setSukliDialog({
      isOpen: true,
      amount
    })
  }

  // Handle sukli confirmation
  const handleSukliConfirmation = async (isGiven: boolean) => {
    if (isGiven) {
      // Record sukli as a negative payment to properly balance the overpayment
      try {
        const response = await fetch('/api/debts', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            customer_name: customerName,
            customer_family_name: customerFamilyName,
            product_name: 'SUKLI_ADJUSTMENT',
            product_price: sukliDialog.amount,
            quantity: 1,
            debt_date: new Date().toISOString().split('T')[0],
            notes: `Sukli adjustment - ₱${sukliDialog.amount.toFixed(2)} change given to customer`
          }),
        })

        if (!response.ok) {
          throw new Error('Failed to record sukli adjustment')
        }

        await fetchCustomerData() // Refresh the data
        onRefresh?.() // Refresh parent component data
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to record sukli adjustment')
      }
    }

    // Close dialog
    setSukliDialog({
      isOpen: false,
      amount: 0
    })
  }

  // Handle customer profile update
  const handleUpdateCustomer = async (updatedData: Partial<Customer>) => {
    if (!customer) return

    try {
      const response = await fetch(`/api/customers/${customer.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(updatedData),
      })

      if (!response.ok) {
        throw new Error('Failed to update customer')
      }

      const result = await response.json()
      setCustomer(result.customer)
    } catch (error) {
      console.error('Error updating customer:', error)
      throw error
    }
  }

  // Handle creating customer profile
  const handleCreateCustomerProfile = async () => {
    try {
      const requestBody = {
        customer_name: customerName,
        customer_family_name: customerFamilyName,
      }

      const response = await fetch('/api/customers', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestBody),
      })

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}))
        console.error('API Error:', errorData)
        throw new Error(errorData.error || `HTTP ${response.status}: Failed to create customer profile`)
      }

      const result = await response.json()
      setCustomer(result.customer)

      // Show success message
      alert('Customer profile created successfully!')
    } catch (error) {
      console.error('Error creating customer profile:', error)
      const errorMessage = error instanceof Error ? error.message : 'Failed to create customer profile'
      alert(`Error: ${errorMessage}`)
    }
  }

  // Calculate summary with proper overpayment handling
  const summary = {
    totalDebt: debts.reduce((sum, debt) => sum + debt.total_amount, 0),
    totalPayments: payments.reduce((sum, payment) => sum + payment.payment_amount, 0),
    debtCount: debts.length,
    paymentCount: payments.length,
    remainingBalance: 0,
    changeAmount: 0,
    isOverpaid: false
  }

  const actualRemaining = summary.totalDebt - summary.totalPayments
  summary.remainingBalance = Math.max(actualRemaining, 0)
  summary.changeAmount = Math.max(-actualRemaining, 0)
  summary.isOverpaid = actualRemaining < 0

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div
        className="w-full max-w-3xl max-h-[85vh] overflow-hidden rounded-xl shadow-2xl"
        style={{
          backgroundColor: resolvedTheme === 'dark' ? '#1e293b' : '#ffffff'
        }}
      >
        {/* Header */}
        <div className="flex items-center justify-between p-5 border-b" style={{
          borderColor: resolvedTheme === 'dark' ? '#334155' : '#e5e7eb'
        }}>
          <div className="flex items-center space-x-4">
            {/* Customer Profile Picture */}
            {customer && (
              <div className="relative">
                <ProfilePictureUpload
                  currentImageUrl={customer.profile_picture_url || ''}
                  currentPublicId={customer.profile_picture_public_id || ''}
                  onImageChange={() => {}} // Read-only in header
                  size="lg"
                  disabled={true}
                />
              </div>
            )}
            <div>
              <h2
                className="text-xl font-semibold"
                style={{
                  color: resolvedTheme === 'dark' ? '#ffffff' : '#111827'
                }}
              >
                Customer Debt Details
              </h2>
              <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                {customerName} {customerFamilyName}
              </p>
              {customer?.birth_date && (
                <p className="text-xs text-gray-500 dark:text-gray-500 mt-0.5">
                  Age: {(() => {
                    const today = new Date()
                    const birth = new Date(customer.birth_date)
                    let age = today.getFullYear() - birth.getFullYear()
                    const monthDiff = today.getMonth() - birth.getMonth()
                    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) {
                      age--
                    }
                    return age
                  })()} years old
                </p>
              )}
            </div>
          </div>
          <button
            onClick={onClose}
            className="p-2 rounded-lg text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
          >
            <X className="h-5 w-5" />
          </button>
        </div>

        {/* Tabs */}
        <div className="flex border-b overflow-x-auto" style={{
          borderColor: resolvedTheme === 'dark' ? '#334155' : '#e5e7eb'
        }}>
          {[
            { id: 'profile', label: 'Profile', icon: User },
            { id: 'summary', label: 'Summary', icon: Eye },
            { id: 'debts', label: `Debts (${summary.debtCount})`, icon: Receipt },
            { id: 'payments', label: `Payments (${summary.paymentCount})`, icon: DollarSign }
          ].map(({ id, label, icon: Icon }) => (
            <button
              key={id}
              onClick={() => setActiveTab(id as 'profile' | 'debts' | 'payments' | 'summary')}
              className={`flex items-center px-4 py-2.5 text-sm font-medium transition-colors whitespace-nowrap ${
                activeTab === id
                  ? 'text-green-600 border-b-2 border-green-600'
                  : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-200'
              }`}
            >
              <Icon className="h-4 w-4 mr-2" />
              {label}
            </button>
          ))}
        </div>

        {/* Content */}
        <div className="p-5 overflow-y-auto max-h-[55vh]">
          {loading ? (
            <div className="flex items-center justify-center py-12">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-green-600"></div>
              <span className="ml-3 text-gray-600 dark:text-gray-400">Loading customer data...</span>
            </div>
          ) : error ? (
            <div className="text-center py-12">
              <div className="text-red-500 mb-2">Error loading data</div>
              <div className="text-sm text-gray-600 dark:text-gray-400">{error}</div>
            </div>
          ) : (
            <>
              {/* Profile Tab */}
              {activeTab === 'profile' && (
                <div className="space-y-6">
                  {customer ? (
                    <CustomerProfile
                      customer={customer}
                      onUpdate={handleUpdateCustomer}
                      isEditable={true}
                    />
                  ) : (
                    <div className="text-center py-12">
                      <User className="h-12 w-12 text-gray-400 mx-auto mb-3" />
                      <p className="text-gray-600 dark:text-gray-400">
                        Customer profile not found. This customer may need to be added to the customer database.
                      </p>
                      <button
                        onClick={handleCreateCustomerProfile}
                        className="mt-4 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
                      >
                        Create Customer Profile
                      </button>
                    </div>
                  )}
                </div>
              )}

              {/* Summary Tab */}
              {activeTab === 'summary' && (
                <div className="space-y-6">
                  {/* Summary Cards */}
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                    <div className="p-4 rounded-lg border" style={{
                      backgroundColor: resolvedTheme === 'dark' ? '#374151' : '#f9fafb',
                      borderColor: resolvedTheme === 'dark' ? '#4b5563' : '#e5e7eb'
                    }}>
                      <div className="flex items-center">
                        <Receipt className="h-8 w-8 text-red-500" />
                        <div className="ml-3">
                          <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Total Debt</p>
                          <p
                            className="text-lg font-semibold"
                            style={{
                              color: resolvedTheme === 'dark' ? '#ffffff' : '#111827'
                            }}
                          >
                            ₱{summary.totalDebt.toFixed(2)}
                          </p>
                        </div>
                      </div>
                    </div>

                    <div className="p-4 rounded-lg border" style={{
                      backgroundColor: resolvedTheme === 'dark' ? '#374151' : '#f9fafb',
                      borderColor: resolvedTheme === 'dark' ? '#4b5563' : '#e5e7eb'
                    }}>
                      <div className="flex items-center">
                        <DollarSign className="h-8 w-8 text-green-500" />
                        <div className="ml-3">
                          <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Total Payments</p>
                          <p
                            className="text-lg font-semibold"
                            style={{
                              color: resolvedTheme === 'dark' ? '#ffffff' : '#111827'
                            }}
                          >
                            ₱{summary.totalPayments.toFixed(2)}
                          </p>
                        </div>
                      </div>
                    </div>

                    <div className="p-4 rounded-lg border" style={{
                      backgroundColor: resolvedTheme === 'dark' ? '#374151' : '#f9fafb',
                      borderColor: resolvedTheme === 'dark' ? '#4b5563' : '#e5e7eb'
                    }}>
                      <div className="flex items-center">
                        <CreditCard className={`h-8 w-8 ${summary.remainingBalance > 0 ? 'text-orange-500' : 'text-green-500'}`} />
                        <div className="ml-3">
                          <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Remaining Balance</p>
                          <p className={`text-lg font-semibold ${summary.remainingBalance > 0 ? 'text-orange-600' : 'text-green-600'}`}>
                            ₱{summary.remainingBalance.toFixed(2)}
                          </p>
                        </div>
                      </div>
                    </div>

                    {/* Show change/sukli for overpayments */}
                    {summary.isOverpaid && summary.changeAmount > 0 && (
                      <div className="p-4 rounded-lg border" style={{
                        backgroundColor: resolvedTheme === 'dark' ? '#1e3a8a' : '#dbeafe',
                        borderColor: resolvedTheme === 'dark' ? '#3b82f6' : '#3b82f6'
                      }}>
                        <div className="flex items-center">
                          <DollarSign className="h-8 w-8 text-blue-500" />
                          <div className="ml-3 flex-1">
                            <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Sukli (Change)</p>
                            <button
                              onClick={() => handleSukliClick(customerName, customerFamilyName, summary.changeAmount)}
                              className="relative group text-lg font-semibold text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 transition-all duration-300 cursor-pointer"
                              title="Click to mark sukli as given"
                            >
                              {/* Rotating border effect */}
                              <div className="absolute inset-0 rounded-lg border-2 border-blue-400 opacity-0 group-hover:opacity-100 group-hover:animate-pulse transition-opacity duration-300"></div>
                              <div className="absolute inset-0 rounded-lg border border-blue-300 opacity-0 group-hover:opacity-50 group-hover:animate-spin transition-opacity duration-300" style={{ animationDuration: '3s' }}></div>

                              {/* Content */}
                              <span className="relative z-10 px-2 py-1 rounded-lg group-hover:bg-blue-50 dark:group-hover:bg-blue-900/20 transition-colors duration-300">
                                ₱{summary.changeAmount.toFixed(2)}
                              </span>
                            </button>
                          </div>
                        </div>
                      </div>
                    )}

                    <div className="p-4 rounded-lg border" style={{
                      backgroundColor: resolvedTheme === 'dark' ? '#374151' : '#f9fafb',
                      borderColor: resolvedTheme === 'dark' ? '#4b5563' : '#e5e7eb'
                    }}>
                      <div className="flex items-center">
                        <Users className="h-8 w-8 text-blue-500" />
                        <div className="ml-3">
                          <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Transactions</p>
                          <p
                            className="text-lg font-semibold"
                            style={{
                              color: resolvedTheme === 'dark' ? '#ffffff' : '#111827'
                            }}
                          >
                            {summary.debtCount + summary.paymentCount}
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Payment Progress */}
                  <div className="p-4 rounded-lg border" style={{
                    backgroundColor: resolvedTheme === 'dark' ? '#374151' : '#f9fafb',
                    borderColor: resolvedTheme === 'dark' ? '#4b5563' : '#e5e7eb'
                  }}>
                    <h3
                      className="text-lg font-semibold mb-3"
                      style={{
                        color: resolvedTheme === 'dark' ? '#ffffff' : '#111827'
                      }}
                    >
                      Payment Progress
                    </h3>
                    <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-3">
                      <div
                        className="bg-green-600 h-3 rounded-full transition-all duration-300"
                        style={{
                          width: `${summary.totalDebt > 0 ? Math.min((summary.totalPayments / summary.totalDebt) * 100, 100) : 0}%`
                        }}
                      ></div>
                    </div>
                    <div className="flex justify-between text-sm text-gray-600 dark:text-gray-400 mt-2">
                      <span>
                        {summary.totalDebt > 0 ? Math.min(((summary.totalPayments / summary.totalDebt) * 100), 100).toFixed(1) : 0}% paid
                      </span>
                      <span>
                        {summary.isOverpaid
                          ? `₱${summary.changeAmount.toFixed(2)} sukli`
                          : summary.remainingBalance > 0
                            ? `₱${summary.remainingBalance.toFixed(2)} remaining`
                            : 'Fully paid'
                        }
                      </span>
                    </div>
                  </div>
                </div>
              )}

              {/* Debts Tab */}
              {activeTab === 'debts' && (
                <div className="space-y-4">
                  <h3
                    className="text-lg font-semibold"
                    style={{
                      color: resolvedTheme === 'dark' ? '#ffffff' : '#111827'
                    }}
                  >
                    Debt Records ({debts.length})
                  </h3>

                  {debts.length === 0 ? (
                    <div className="text-center py-8">
                      <Receipt className="h-12 w-12 text-gray-400 mx-auto mb-3" />
                      <p className="text-gray-600 dark:text-gray-400">No debt records found</p>
                    </div>
                  ) : (
                    <div className="space-y-3">
                      {debts.map((debt) => (
                        <div
                          key={debt.id}
                          className="p-4 rounded-lg border"
                          style={{
                            backgroundColor: resolvedTheme === 'dark' ? '#374151' : '#f9fafb',
                            borderColor: resolvedTheme === 'dark' ? '#4b5563' : '#e5e7eb'
                          }}
                        >
                          <div className="flex justify-between items-start">
                            <div className="flex-1">
                              <div className="flex items-center mb-2">
                                <Package className="h-4 w-4 text-blue-500 mr-2" />
                                <h4
                                  className="font-semibold"
                                  style={{
                                    color: resolvedTheme === 'dark' ? '#ffffff' : '#111827'
                                  }}
                                >
                                  {debt.product_name}
                                </h4>
                              </div>

                              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                                <div>
                                  <span className="text-gray-600 dark:text-gray-400">Price:</span>
                                  <p
                                    className="font-medium"
                                    style={{
                                      color: resolvedTheme === 'dark' ? '#ffffff' : '#111827'
                                    }}
                                  >
                                    ₱{debt.product_price.toFixed(2)}
                                  </p>
                                </div>
                                <div>
                                  <span className="text-gray-600 dark:text-gray-400">Quantity:</span>
                                  <p
                                    className="font-medium"
                                    style={{
                                      color: resolvedTheme === 'dark' ? '#ffffff' : '#111827'
                                    }}
                                  >
                                    {debt.quantity}
                                  </p>
                                </div>
                                <div>
                                  <span className="text-gray-600 dark:text-gray-400">Total:</span>
                                  <p className="font-semibold text-red-600">₱{debt.total_amount.toFixed(2)}</p>
                                </div>
                                <div>
                                  <span className="text-gray-600 dark:text-gray-400">Date:</span>
                                  <p
                                    className="font-medium"
                                    style={{
                                      color: resolvedTheme === 'dark' ? '#ffffff' : '#111827'
                                    }}
                                  >
                                    {new Date(debt.debt_date).toLocaleDateString()}
                                  </p>
                                </div>
                              </div>

                              {debt.notes && (
                                <div className="mt-3 flex items-start">
                                  <FileText className="h-4 w-4 text-gray-500 mr-2 mt-0.5" />
                                  <p className="text-sm text-gray-600 dark:text-gray-400">{debt.notes}</p>
                                </div>
                              )}
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              )}

              {/* Payments Tab */}
              {activeTab === 'payments' && (
                <div className="space-y-4">
                  <h3
                    className="text-lg font-semibold"
                    style={{
                      color: resolvedTheme === 'dark' ? '#ffffff' : '#111827'
                    }}
                  >
                    Payment Records ({payments.length})
                  </h3>

                  {payments.length === 0 ? (
                    <div className="text-center py-8">
                      <DollarSign className="h-12 w-12 text-gray-400 mx-auto mb-3" />
                      <p className="text-gray-600 dark:text-gray-400">No payment records found</p>
                    </div>
                  ) : (
                    <div className="space-y-3">
                      {payments.map((payment) => (
                        <div
                          key={payment.id}
                          className="p-4 rounded-lg border"
                          style={{
                            backgroundColor: resolvedTheme === 'dark' ? '#374151' : '#f9fafb',
                            borderColor: resolvedTheme === 'dark' ? '#4b5563' : '#e5e7eb'
                          }}
                        >
                          <div className="flex justify-between items-start">
                            <div className="flex-1">
                              <div className="flex items-center mb-2">
                                <DollarSign className="h-4 w-4 text-green-500 mr-2" />
                                <h4 className="font-semibold text-green-600">
                                  ₱{payment.payment_amount.toFixed(2)}
                                </h4>
                              </div>

                              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                                <div>
                                  <span className="text-gray-600 dark:text-gray-400">Date:</span>
                                  <p
                                    className="font-medium"
                                    style={{
                                      color: resolvedTheme === 'dark' ? '#ffffff' : '#111827'
                                    }}
                                  >
                                    {new Date(payment.payment_date).toLocaleDateString()}
                                  </p>
                                </div>
                                <div>
                                  <span className="text-gray-600 dark:text-gray-400">Method:</span>
                                  <p
                                    className="font-medium"
                                    style={{
                                      color: resolvedTheme === 'dark' ? '#ffffff' : '#111827'
                                    }}
                                  >
                                    {payment.payment_method}
                                  </p>
                                </div>
                                <div>
                                  <span className="text-gray-600 dark:text-gray-400">Paid by:</span>
                                  <p
                                    className="font-medium"
                                    style={{
                                      color: resolvedTheme === 'dark' ? '#ffffff' : '#111827'
                                    }}
                                  >
                                    {payment.responsible_family_member || `${customerName} ${customerFamilyName}`}
                                  </p>
                                </div>
                                <div>
                                  <span className="text-gray-600 dark:text-gray-400">Time:</span>
                                  <p
                                    className="font-medium"
                                    style={{
                                      color: resolvedTheme === 'dark' ? '#ffffff' : '#111827'
                                    }}
                                  >
                                    {new Date(payment.created_at).toLocaleTimeString()}
                                  </p>
                                </div>
                              </div>

                              {payment.notes && (
                                <div className="mt-3 flex items-start">
                                  <FileText className="h-4 w-4 text-gray-500 mr-2 mt-0.5" />
                                  <p className="text-sm text-gray-600 dark:text-gray-400">{payment.notes}</p>
                                </div>
                              )}

                              {payment.responsible_family_member && (
                                <div className="mt-2 flex items-center">
                                  <Users className="h-4 w-4 text-blue-500 mr-2" />
                                  <span className="text-sm text-blue-600 dark:text-blue-400 font-medium">
                                    Family member payment
                                  </span>
                                </div>
                              )}
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              )}
            </>
          )}
        </div>
      </div>

      {/* Sukli Confirmation Dialog */}
      {sukliDialog.isOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div
            className="bg-white dark:bg-gray-800 rounded-lg p-6 max-w-md w-full mx-4 shadow-xl"
            style={{
              backgroundColor: resolvedTheme === 'dark' ? '#1f2937' : '#ffffff',
              border: resolvedTheme === 'dark' ? '1px solid #374151' : '1px solid #e5e7eb'
            }}
          >
            <div className="text-center">
              {/* Icon */}
              <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-blue-100 dark:bg-blue-900 mb-4">
                <DollarSign className="h-6 w-6 text-blue-600 dark:text-blue-400" />
              </div>

              {/* Title */}
              <h3
                className="text-lg font-semibold mb-2"
                style={{
                  color: resolvedTheme === 'dark' ? '#ffffff' : '#111827'
                }}
              >
                Sukli Confirmation
              </h3>

              {/* Message */}
              <p
                className="text-sm mb-6"
                style={{
                  color: resolvedTheme === 'dark' ? '#d1d5db' : '#6b7280'
                }}
              >
                Nabigay na ba ang sukli na{' '}
                <span className="font-bold text-blue-600 dark:text-blue-400">
                  ₱{sukliDialog.amount.toFixed(2)}
                </span>{' '}
                kay{' '}
                <span className="font-semibold">
                  {customerName} {customerFamilyName}
                </span>
                ?
              </p>

              {/* Buttons */}
              <div className="flex space-x-3">
                <button
                  onClick={() => handleSukliConfirmation(false)}
                  className="flex-1 px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 rounded-lg transition-colors"
                >
                  Hindi pa
                </button>
                <button
                  onClick={() => handleSukliConfirmation(true)}
                  className="flex-1 px-4 py-2 text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 rounded-lg transition-colors"
                >
                  Oo, nabigay na
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
