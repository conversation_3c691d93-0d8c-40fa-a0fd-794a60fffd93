'use client'

import {
  User,
  MapPin,
  Phone,
  Edit3,
  Save,
  X,
  Cake,
  Home
} from 'lucide-react'
import { useTheme } from 'next-themes'
import { useState } from 'react'

import { Customer } from '@/lib/supabase'

import ProfilePictureUpload from './ProfilePictureUpload'

interface CustomerProfileProps {
  customer: Customer
  onUpdate?: (updatedCustomer: Partial<Customer>) => Promise<void>
  isEditable?: boolean
  compact?: boolean
}

export default function CustomerProfile({
  customer,
  onUpdate,
  isEditable = false,
  compact = false
}: CustomerProfileProps) {
  const { resolvedTheme } = useTheme()
  const [isEditing, setIsEditing] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [formData, setFormData] = useState({
    customer_name: customer.customer_name,
    customer_family_name: customer.customer_family_name,
    phone_number: customer.phone_number || '',
    address: customer.address || '',
    birth_date: customer.birth_date || '',
    birth_place: customer.birth_place || '',
    profile_picture_url: customer.profile_picture_url || '',
    profile_picture_public_id: customer.profile_picture_public_id || '',
    notes: customer.notes || ''
  })

  const handleSave = async () => {
    if (!onUpdate) return

    setIsLoading(true)
    try {
      await onUpdate(formData)
      setIsEditing(false)
    } catch (error) {
      console.error('Failed to update customer:', error)
      alert('Failed to update customer profile')
    } finally {
      setIsLoading(false)
    }
  }

  const handleCancel = () => {
    setFormData({
      customer_name: customer.customer_name,
      customer_family_name: customer.customer_family_name,
      phone_number: customer.phone_number || '',
      address: customer.address || '',
      birth_date: customer.birth_date || '',
      birth_place: customer.birth_place || '',
      profile_picture_url: customer.profile_picture_url || '',
      profile_picture_public_id: customer.profile_picture_public_id || '',
      notes: customer.notes || ''
    })
    setIsEditing(false)
  }

  const handleImageChange = (imageUrl: string | null, publicId: string | null) => {
    setFormData(prev => ({
      ...prev,
      profile_picture_url: imageUrl || '',
      profile_picture_public_id: publicId || ''
    }))
  }

  const formatDate = (dateString: string) => {
    if (!dateString) return 'Not specified'
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    })
  }

  const calculateAge = (birthDate: string) => {
    if (!birthDate) return null
    const today = new Date()
    const birth = new Date(birthDate)
    let age = today.getFullYear() - birth.getFullYear()
    const monthDiff = today.getMonth() - birth.getMonth()
    
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) {
      age--
    }
    
    return age
  }

  if (compact) {
    return (
      <div className="flex items-center space-x-4">
        <ProfilePictureUpload
          currentImageUrl={customer.profile_picture_url || ''}
          currentPublicId={customer.profile_picture_public_id || ''}
          onImageChange={() => {}} // Read-only in compact mode
          size="md"
          disabled={true}
        />
        <div>
          <h3 className="font-semibold text-lg" style={{
            color: resolvedTheme === 'dark' ? '#ffffff' : '#111827'
          }}>
            {customer.customer_name} {customer.customer_family_name}
          </h3>
          {customer.address && (
            <p className="text-sm text-gray-600 dark:text-gray-400 flex items-center">
              <MapPin className="h-4 w-4 mr-1" />
              {customer.address}
            </p>
          )}
        </div>
      </div>
    )
  }

  return (
    <div
      className="bg-white dark:bg-slate-800 rounded-xl p-6 shadow-lg border-0 ring-1 ring-gray-200 dark:ring-slate-600"
      style={{
        background: resolvedTheme === 'dark'
          ? 'linear-gradient(135deg, #1e293b 0%, #334155 100%)'
          : 'linear-gradient(135deg, #ffffff 0%, #f8fafc 100%)'
      }}
    >
      {/* Header */}
      <div className="flex items-start justify-between mb-8">
        <div className="flex items-center gap-6">
          <div className="relative">
            <ProfilePictureUpload
              currentImageUrl={formData.profile_picture_url}
              currentPublicId={formData.profile_picture_public_id}
              onImageChange={handleImageChange}
              size="xl"
              disabled={!isEditing}
            />
            {!isEditing && (
              <div className="absolute -bottom-2 -right-2 w-8 h-8 bg-blue-500 rounded-full border-3 border-white dark:border-slate-800 flex items-center justify-center shadow-lg">
                <User className="w-4 h-4 text-white" />
              </div>
            )}
          </div>
          <div className="space-y-2">
            {isEditing ? (
              <div className="space-y-3">
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
                  <input
                    type="text"
                    value={formData.customer_name}
                    onChange={(e) => setFormData(prev => ({ ...prev, customer_name: e.target.value }))}
                    className="px-3 py-2 border rounded-lg text-lg font-semibold focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                    style={{
                      backgroundColor: resolvedTheme === 'dark' ? '#374151' : '#ffffff',
                      borderColor: resolvedTheme === 'dark' ? '#4b5563' : '#d1d5db',
                      color: resolvedTheme === 'dark' ? '#ffffff' : '#111827'
                    }}
                    placeholder="First Name"
                  />
                  <input
                    type="text"
                    value={formData.customer_family_name}
                    onChange={(e) => setFormData(prev => ({ ...prev, customer_family_name: e.target.value }))}
                    className="px-3 py-2 border rounded-lg text-lg font-semibold focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                    style={{
                      backgroundColor: resolvedTheme === 'dark' ? '#374151' : '#ffffff',
                      borderColor: resolvedTheme === 'dark' ? '#4b5563' : '#d1d5db',
                      color: resolvedTheme === 'dark' ? '#ffffff' : '#111827'
                    }}
                    placeholder="Last Name"
                  />
                </div>
              </div>
            ) : (
              <div className="space-y-2">
                <h2 className="text-3xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                  {customer.customer_name} {customer.customer_family_name}
                </h2>
                {customer.birth_date && (
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400 flex items-center">
                    <Cake className="w-4 h-4 mr-2" />
                    Age: {calculateAge(customer.birth_date)} years old
                  </p>
                )}
              </div>
            )}
          </div>
        </div>

        {isEditable && (
          <div className="flex items-center gap-3">
            {isEditing ? (
              <>
                <button
                  onClick={handleCancel}
                  disabled={isLoading}
                  className="px-4 py-2 bg-gray-500 hover:bg-gray-600 text-white rounded-lg disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2 transition-all duration-200 shadow-sm hover:shadow-md"
                >
                  <X className="h-4 w-4" />
                  <span>Cancel</span>
                </button>
                <button
                  onClick={handleSave}
                  disabled={isLoading}
                  className="px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2 transition-all duration-200 shadow-sm hover:shadow-md"
                >
                  <Save className="h-4 w-4" />
                  <span>{isLoading ? 'Saving...' : 'Save'}</span>
                </button>
              </>
            ) : (
              <button
                onClick={() => setIsEditing(true)}
                className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg flex items-center gap-2 transition-all duration-200 shadow-sm hover:shadow-md"
              >
                <Edit3 className="h-4 w-4" />
                <span>Edit Profile</span>
              </button>
            )}
          </div>
        )}
      </div>

      {/* Profile Information Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Contact Information */}
        <div className="bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-slate-700 dark:to-slate-600 rounded-xl p-6 border border-blue-100 dark:border-slate-500">
          <h3 className="text-lg font-bold text-blue-900 dark:text-blue-100 flex items-center mb-6">
            <div className="w-10 h-10 bg-blue-500 rounded-lg flex items-center justify-center mr-3">
              <Phone className="h-5 w-5 text-white" />
            </div>
            Contact Information
          </h3>

          <div className="space-y-5">
            <div className="bg-white dark:bg-slate-800 rounded-lg p-4 shadow-sm">
              <label className="block text-sm font-semibold text-gray-700 dark:text-gray-300 mb-2">
                📞 Phone Number
              </label>
              {isEditing ? (
                <input
                  type="tel"
                  value={formData.phone_number}
                  onChange={(e) => setFormData(prev => ({ ...prev, phone_number: e.target.value }))}
                  className="w-full px-4 py-3 border rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200"
                  style={{
                    backgroundColor: resolvedTheme === 'dark' ? '#334155' : '#ffffff',
                    borderColor: resolvedTheme === 'dark' ? '#475569' : '#d1d5db',
                    color: resolvedTheme === 'dark' ? '#ffffff' : '#111827'
                  }}
                  placeholder="Enter phone number"
                />
              ) : (
                <p className="text-lg font-medium text-gray-900 dark:text-white">
                  {customer.phone_number || (
                    <span className="text-gray-500 italic">Not specified</span>
                  )}
                </p>
              )}
            </div>

            <div className="bg-white dark:bg-slate-800 rounded-lg p-4 shadow-sm">
              <label className="block text-sm font-semibold text-gray-700 dark:text-gray-300 mb-2">
                🏠 Address
              </label>
              {isEditing ? (
                <textarea
                  value={formData.address}
                  onChange={(e) => setFormData(prev => ({ ...prev, address: e.target.value }))}
                  rows={3}
                  className="w-full px-4 py-3 border rounded-xl resize-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200"
                  style={{
                    backgroundColor: resolvedTheme === 'dark' ? '#334155' : '#ffffff',
                    borderColor: resolvedTheme === 'dark' ? '#475569' : '#d1d5db',
                    color: resolvedTheme === 'dark' ? '#ffffff' : '#111827'
                  }}
                  placeholder="Enter address"
                />
              ) : (
                <p className="text-lg font-medium text-gray-900 dark:text-white">
                  {customer.address || (
                    <span className="text-gray-500 italic">Not specified</span>
                  )}
                </p>
              )}
            </div>
          </div>
        </div>

        {/* Personal Information */}
        <div className="bg-gradient-to-br from-purple-50 to-pink-50 dark:from-slate-700 dark:to-slate-600 rounded-xl p-6 border border-purple-100 dark:border-slate-500">
          <h3 className="text-lg font-bold text-purple-900 dark:text-purple-100 flex items-center mb-6">
            <div className="w-10 h-10 bg-purple-500 rounded-lg flex items-center justify-center mr-3">
              <User className="h-5 w-5 text-white" />
            </div>
            Personal Information
          </h3>

          <div className="space-y-5">
            <div className="bg-white dark:bg-slate-800 rounded-lg p-4 shadow-sm">
              <label className="block text-sm font-semibold text-gray-700 dark:text-gray-300 mb-2">
                🎂 Birth Date
              </label>
              {isEditing ? (
                <input
                  type="date"
                  value={formData.birth_date}
                  onChange={(e) => setFormData(prev => ({ ...prev, birth_date: e.target.value }))}
                  className="w-full px-4 py-3 border rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-all duration-200"
                  style={{
                    backgroundColor: resolvedTheme === 'dark' ? '#334155' : '#ffffff',
                    borderColor: resolvedTheme === 'dark' ? '#475569' : '#d1d5db',
                    color: resolvedTheme === 'dark' ? '#ffffff' : '#111827'
                  }}
                />
              ) : (
                <div className="space-y-1">
                  <p className="text-lg font-medium text-gray-900 dark:text-white">
                    {formatDate(customer.birth_date || '')}
                  </p>
                  {customer.birth_date && (
                    <p className="text-sm text-purple-600 dark:text-purple-400 font-medium">
                      {calculateAge(customer.birth_date)} years old
                    </p>
                  )}
                </div>
              )}
            </div>

            <div className="bg-white dark:bg-slate-800 rounded-lg p-4 shadow-sm">
              <label className="block text-sm font-semibold text-gray-700 dark:text-gray-300 mb-2">
                🏙️ Birth Place
              </label>
              {isEditing ? (
                <input
                  type="text"
                  value={formData.birth_place}
                  onChange={(e) => setFormData(prev => ({ ...prev, birth_place: e.target.value }))}
                  className="w-full px-4 py-3 border rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-all duration-200"
                  style={{
                    backgroundColor: resolvedTheme === 'dark' ? '#334155' : '#ffffff',
                    borderColor: resolvedTheme === 'dark' ? '#475569' : '#d1d5db',
                    color: resolvedTheme === 'dark' ? '#ffffff' : '#111827'
                  }}
                  placeholder="Enter birthplace"
                />
              ) : (
                <p className="text-lg font-medium text-gray-900 dark:text-white">
                  {customer.birth_place || (
                    <span className="text-gray-500 italic">Not specified</span>
                  )}
                </p>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Notes Section */}
      {(isEditing || customer.notes) && (
        <div className="mt-8">
          <div className="bg-gradient-to-br from-amber-50 to-orange-50 dark:from-slate-700 dark:to-slate-600 rounded-xl p-6 border border-amber-100 dark:border-slate-500">
            <h3 className="text-lg font-bold text-amber-900 dark:text-amber-100 flex items-center mb-4">
              <div className="w-10 h-10 bg-amber-500 rounded-lg flex items-center justify-center mr-3">
                <FileText className="h-5 w-5 text-white" />
              </div>
              📝 Notes
            </h3>
            {isEditing ? (
              <textarea
                value={formData.notes}
                onChange={(e) => setFormData(prev => ({ ...prev, notes: e.target.value }))}
                rows={4}
                className="w-full px-4 py-3 border rounded-xl resize-none focus:ring-2 focus:ring-amber-500 focus:border-amber-500 transition-all duration-200"
                style={{
                  backgroundColor: resolvedTheme === 'dark' ? '#334155' : '#ffffff',
                  borderColor: resolvedTheme === 'dark' ? '#475569' : '#d1d5db',
                  color: resolvedTheme === 'dark' ? '#ffffff' : '#111827'
                }}
                placeholder="Add notes about this customer..."
              />
            ) : (
              <div className="bg-white dark:bg-slate-800 rounded-lg p-4 shadow-sm">
                <p className="text-gray-900 dark:text-white whitespace-pre-wrap text-lg leading-relaxed">
                  {customer.notes || (
                    <span className="text-gray-500 italic">No notes available</span>
                  )}
                </p>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  )
}
