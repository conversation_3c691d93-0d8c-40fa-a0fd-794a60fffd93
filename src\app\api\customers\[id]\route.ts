import { NextRequest, NextResponse } from 'next/server'

import { supabase } from '@/lib/supabase'

// GET - Fetch single customer
export async function GET(
  _request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params
    const { data: customer, error } = await supabase
      .from('customers')
      .select('*')
      .eq('id', id)
      .single()

    if (error) {
      return NextResponse.json({ error: error.message }, { status: 500 })
    }

    if (!customer) {
      return NextResponse.json({ error: 'Customer not found' }, { status: 404 })
    }

    return NextResponse.json({ customer })
  } catch {
    return NextResponse.json(
      { error: 'Failed to fetch customer' },
      { status: 500 }
    )
  }
}

// PUT - Update customer
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params
    const body = await request.json()
    const {
      customer_name,
      customer_family_name,
      profile_picture_url,
      profile_picture_public_id,
      phone_number,
      address,
      birth_date,
      birth_place,
      notes,
    } = body



    // Validate required fields
    if (!customer_name || !customer_family_name) {
      return NextResponse.json(
        { error: 'Customer name and family name are required' },
        { status: 400 }
      )
    }

    // Build update object dynamically - standardize all field handling
    const updateData: Record<string, string | null> = {
      customer_name,
      customer_family_name,
      profile_picture_url: profile_picture_url || null,
      profile_picture_public_id: profile_picture_public_id || null,
      notes: notes || null,
    }

    // Add all optional fields with consistent logic
    if (phone_number !== undefined) {
      updateData.phone_number = phone_number || null
    }
    if (address !== undefined) {
      updateData.address = address || null
    }
    if (birth_date !== undefined) {
      updateData.birth_date = birth_date || null
    }
    if (birth_place !== undefined) {
      updateData.birth_place = birth_place || null
    }

    const { data: customer, error } = await supabase
      .from('customers')
      .update(updateData)
      .eq('id', id)
      .select()
      .single()

    if (error) {
      console.error('Supabase update error:', error)
      return NextResponse.json({ error: error.message }, { status: 500 })
    }

    return NextResponse.json({ customer })
  } catch (error) {
    console.error('Customer update API error:', error)
    return NextResponse.json(
      { error: 'Failed to update customer' },
      { status: 500 }
    )
  }
}

// DELETE - Delete customer
export async function DELETE(
  _request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params
    


    const { error } = await supabase
      .from('customers')
      .delete()
      .eq('id', id)

    if (error) {
      return NextResponse.json({ error: error.message }, { status: 500 })
    }

    return NextResponse.json({ message: 'Customer deleted successfully' })
  } catch {
    return NextResponse.json(
      { error: 'Failed to delete customer' },
      { status: 500 }
    )
  }
}
