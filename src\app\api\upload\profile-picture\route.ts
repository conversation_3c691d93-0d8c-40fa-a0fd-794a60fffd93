import { v2 as cloudinary } from 'cloudinary'
import { NextRequest, NextResponse } from 'next/server'

// Configure Cloudinary
cloudinary.config({
  cloud_name: process.env.NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME || '',
  api_key: process.env.CLOUDINARY_API_KEY || '',
  api_secret: process.env.CLOUDINARY_API_SECRET || '',
})

export async function POST(request: NextRequest) {
  try {
    // Check Cloudinary configuration
    if (!process.env.NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME || !process.env.CLOUDINARY_API_KEY || !process.env.CLOUDINARY_API_SECRET) {
      console.error('Missing Cloudinary configuration:', {
        cloudName: !!process.env.NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME,
        apiKey: !!process.env.CLOUDINARY_API_KEY,
        apiSecret: !!process.env.CLOUDINARY_API_SECRET
      })
      return NextResponse.json({ error: 'Server configuration error: Missing Cloudinary credentials' }, { status: 500 })
    }

    const formData = await request.formData()
    const file = formData.get('file') as File
    const oldPublicId = formData.get('old_public_id') as string | null

    if (!file) {
      return NextResponse.json({ error: 'No file uploaded' }, { status: 400 })
    }

    // Validate file type
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp']
    if (!allowedTypes.includes(file.type)) {
      return NextResponse.json(
        { error: 'Invalid file type. Only JPEG, PNG, and WebP are allowed.' },
        { status: 400 }
      )
    }

    // Validate file size (max 5MB)
    const maxSize = 5 * 1024 * 1024 // 5MB
    if (file.size > maxSize) {
      return NextResponse.json(
        { error: 'File too large. Maximum size is 5MB.' },
        { status: 400 }
      )
    }

    // Convert file to buffer for Cloudinary upload
    const bytes = await file.arrayBuffer()
    const buffer = Buffer.from(bytes)

    // Create unique public_id for Cloudinary
    const timestamp = Date.now()
    const randomString = Math.random().toString(36).substring(2, 15)
    const publicId = `customer_profiles/profile_${timestamp}_${randomString}`

    // Upload to Cloudinary with enhanced transformations for better quality
    const uploadResult = await new Promise((resolve, reject) => {
      cloudinary.uploader.upload_stream(
        {
          resource_type: 'image',
          public_id: publicId,
          folder: 'customer_profiles',
          transformation: [
            // Enhanced transformations for professional profile pictures
            {
              width: 500,
              height: 500,
              crop: 'fill',
              gravity: 'face',
              quality: 'auto:good',
              fetch_format: 'auto',
              dpr: 'auto',
              flags: 'progressive'
            },
            // Additional optimization for web display
            {
              effect: 'sharpen:100',
              quality: 85
            }
          ],
          // Enable automatic format optimization
          flags: 'progressive',
          // Set appropriate quality
          quality: 'auto:good'
        },
        (error, result) => {
          if (error) {
            console.error('Cloudinary upload error:', error)
            reject(new Error(`Cloudinary upload failed: ${error.message}`))
          } else if (result) {
            resolve(result)
          } else {
            reject(new Error('Upload failed: No result returned from Cloudinary'))
          }
        }
      ).end(buffer)
    })

    const result = uploadResult as { secure_url: string; public_id: string }

    // Delete old image from Cloudinary if provided
    if (oldPublicId && oldPublicId.trim() !== '') {
      try {
        console.log('Attempting to delete old image:', oldPublicId)
        const deleteResult = await cloudinary.uploader.destroy(oldPublicId)
        console.log('Delete result:', deleteResult)

        if (deleteResult.result === 'ok') {
          console.log('Successfully deleted old profile picture:', oldPublicId)
        } else if (deleteResult.result === 'not found') {
          console.log('Old profile picture not found in Cloudinary (may have been already deleted):', oldPublicId)
        } else {
          console.warn('Failed to delete old profile picture:', deleteResult)
        }
      } catch (deleteError) {
        // Log the error but don't fail the upload
        console.error('Error deleting old profile picture:', deleteError)
        console.log('Upload will continue despite deletion error')
      }
    }

    return NextResponse.json({
      success: true,
      url: result.secure_url,
      public_id: result.public_id,
      filename: result.public_id,
      old_image_deleted: oldPublicId ? true : false
    })

  } catch (error) {
    console.error('Error uploading file to Cloudinary:', error)
    return NextResponse.json(
      { error: 'Failed to upload file to cloud storage' },
      { status: 500 }
    )
  }
}

// DELETE - Remove profile picture from Cloudinary
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const publicId = searchParams.get('public_id')

    if (!publicId) {
      return NextResponse.json({ error: 'Public ID is required' }, { status: 400 })
    }

    // Delete from Cloudinary
    const result = await cloudinary.uploader.destroy(publicId)

    if (result.result === 'ok') {
      return NextResponse.json({
        success: true,
        message: 'Profile picture deleted successfully from cloud storage'
      })
    } else {
      return NextResponse.json({
        error: 'Failed to delete image from cloud storage'
      }, { status: 400 })
    }

  } catch (error) {
    console.error('Error deleting file from Cloudinary:', error)
    return NextResponse.json(
      { error: 'Failed to delete file from cloud storage' },
      { status: 500 }
    )
  }
}
